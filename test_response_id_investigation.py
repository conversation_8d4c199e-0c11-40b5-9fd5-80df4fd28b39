#!/usr/bin/env python3
"""
Response ID Investigation Test for SecuraMind

This test investigates the Response ID mismatch issue to determine if it's:
1. A genuine data isolation vulnerability
2. A flawed validation mechanism
3. An implementation bug in the ID checking logic

The test focuses on concurrent request scenarios where Response ID mismatches
would most likely occur if there's a real data leakage issue.
"""

import asyncio
import aiohttp
import json
import time
import uuid
from typing import List, Dict, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TestRequest:
    """Represents a test request with unique identifiers"""
    test_id: str
    query: str
    collection_name: str
    expected_response_pattern: str
    
@dataclass
class TestResult:
    """Represents the result of a test request"""
    test_id: str
    success: bool
    response_tokens: List[str]
    error_message: str = ""
    response_time: float = 0.0
    response_id_errors: List[str] = None

class ResponseIDInvestigator:
    """Investigates Response ID mismatch issues in SecuraMind"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def setup_test_data(self) -> bool:
        """Setup test collections with unique data for isolation testing"""
        test_collections = [
            {
                "collection_name": "user_a_data",
                "source_identifier": "test_user_a",
                "source_name": "User A Test Data",
                "text_content": "User A's secret information: ALPHA-SECRET-123. This data belongs to User A only."
            },
            {
                "collection_name": "user_b_data", 
                "source_identifier": "test_user_b",
                "source_name": "User B Test Data",
                "text_content": "User B's confidential data: BETA-CONFIDENTIAL-456. This data belongs to User B only."
            },
            {
                "collection_name": "user_c_data",
                "source_identifier": "test_user_c", 
                "source_name": "User C Test Data",
                "text_content": "User C's private information: GAMMA-PRIVATE-789. This data belongs to User C only."
            }
        ]
        
        logger.info("Setting up test data collections...")
        
        for collection_data in test_collections:
            try:
                async with self.session.post(
                    f"{self.base_url}/api/v1/ingest/text",
                    json=collection_data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status != 200:
                        logger.error(f"Failed to create collection {collection_data['collection_name']}: {response.status}")
                        return False
                    result = await response.json()
                    logger.info(f"Created collection {collection_data['collection_name']}: {result}")
            except Exception as e:
                logger.error(f"Error creating collection {collection_data['collection_name']}: {e}")
                return False
        
        # Wait for ingestion to complete
        await asyncio.sleep(2)
        return True
    
    async def send_inference_request(self, test_request: TestRequest) -> TestResult:
        """Send a single inference request and capture detailed response information"""
        start_time = time.time()
        tokens = []
        response_id_errors = []
        
        request_payload = {
            "query": test_request.query,
            "collection_name": test_request.collection_name,
            "config": {
                "max_new_tokens": 100,
                "temperature": 0.1  # Low temperature for consistent responses
            },
            "language": "en"
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/inference",
                json=request_payload,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "text/event-stream"
                }
            ) as response:
                
                if response.status != 200:
                    return TestResult(
                        test_id=test_request.test_id,
                        success=False,
                        response_tokens=[],
                        error_message=f"HTTP {response.status}: {await response.text()}",
                        response_time=time.time() - start_time
                    )
                
                # Process streaming response
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])  # Remove 'data: ' prefix
                            
                            if 'token' in data:
                                tokens.append(data['token'])
                            elif 'error' in data:
                                if 'Response ID mismatch' in data['error'] or 'ID mismatch' in data['error']:
                                    response_id_errors.append(data['error'])
                                return TestResult(
                                    test_id=test_request.test_id,
                                    success=False,
                                    response_tokens=tokens,
                                    error_message=data['error'],
                                    response_time=time.time() - start_time,
                                    response_id_errors=response_id_errors
                                )
                            elif 'event' in data and data['event'] == 'eos':
                                break
                                
                        except json.JSONDecodeError:
                            continue
                
                response_text = ''.join(tokens)
                success = test_request.expected_response_pattern.lower() in response_text.lower()
                
                return TestResult(
                    test_id=test_request.test_id,
                    success=success,
                    response_tokens=tokens,
                    response_time=time.time() - start_time,
                    response_id_errors=response_id_errors
                )
                
        except Exception as e:
            return TestResult(
                test_id=test_request.test_id,
                success=False,
                response_tokens=tokens,
                error_message=str(e),
                response_time=time.time() - start_time,
                response_id_errors=response_id_errors
            )
    
    async def test_concurrent_requests(self, num_concurrent: int = 10) -> List[TestResult]:
        """Test concurrent requests to detect Response ID mismatch issues"""
        logger.info(f"Starting concurrent request test with {num_concurrent} requests...")
        
        # Create test requests that should return different data
        test_requests = []
        for i in range(num_concurrent):
            user_type = ['a', 'b', 'c'][i % 3]
            secret_pattern = ['ALPHA-SECRET', 'BETA-CONFIDENTIAL', 'GAMMA-PRIVATE'][i % 3]
            
            test_requests.append(TestRequest(
                test_id=f"concurrent_test_{i}_{uuid.uuid4().hex[:8]}",
                query=f"What is the secret information for user {user_type.upper()}?",
                collection_name=f"user_{user_type}_data",
                expected_response_pattern=secret_pattern
            ))
        
        # Send all requests concurrently
        tasks = [self.send_inference_request(req) for req in test_requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error results
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(TestResult(
                    test_id=test_requests[i].test_id,
                    success=False,
                    response_tokens=[],
                    error_message=str(result),
                    response_id_errors=[]
                ))
            else:
                final_results.append(result)
        
        return final_results
    
    def analyze_results(self, results: List[TestResult]) -> Dict[str, Any]:
        """Analyze test results for Response ID issues and data isolation problems"""
        analysis = {
            "total_requests": len(results),
            "successful_requests": 0,
            "failed_requests": 0,
            "response_id_errors": [],
            "data_isolation_violations": [],
            "cross_contamination_detected": False,
            "average_response_time": 0.0,
            "error_summary": {}
        }
        
        total_time = 0
        error_counts = {}
        
        for result in results:
            total_time += result.response_time
            
            if result.success:
                analysis["successful_requests"] += 1
            else:
                analysis["failed_requests"] += 1
                
                # Count error types
                error_type = result.error_message.split(':')[0] if result.error_message else "Unknown"
                error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
            # Check for Response ID errors
            if result.response_id_errors:
                analysis["response_id_errors"].extend(result.response_id_errors)
            
            # Check for data isolation violations (cross-contamination)
            response_text = ''.join(result.response_tokens).lower()
            if 'alpha-secret' in response_text and 'user_a' not in result.test_id:
                analysis["data_isolation_violations"].append(f"User A data leaked to {result.test_id}")
                analysis["cross_contamination_detected"] = True
            elif 'beta-confidential' in response_text and 'user_b' not in result.test_id:
                analysis["data_isolation_violations"].append(f"User B data leaked to {result.test_id}")
                analysis["cross_contamination_detected"] = True
            elif 'gamma-private' in response_text and 'user_c' not in result.test_id:
                analysis["data_isolation_violations"].append(f"User C data leaked to {result.test_id}")
                analysis["cross_contamination_detected"] = True
        
        analysis["average_response_time"] = total_time / len(results) if results else 0
        analysis["error_summary"] = error_counts
        
        return analysis

async def main():
    """Main test execution function"""
    logger.info("Starting Response ID Investigation Test")
    
    async with ResponseIDInvestigator() as investigator:
        # Setup test data
        if not await investigator.setup_test_data():
            logger.error("Failed to setup test data. Exiting.")
            return
        
        # Test 1: Sequential requests (baseline)
        logger.info("\n=== Test 1: Sequential Requests (Baseline) ===")
        sequential_results = []
        for i in range(3):
            user_type = ['a', 'b', 'c'][i]
            secret_pattern = ['ALPHA-SECRET', 'BETA-CONFIDENTIAL', 'GAMMA-PRIVATE'][i]
            
            request = TestRequest(
                test_id=f"sequential_test_{i}",
                query=f"What is the secret information?",
                collection_name=f"user_{user_type}_data",
                expected_response_pattern=secret_pattern
            )
            
            result = await investigator.send_inference_request(request)
            sequential_results.append(result)
            logger.info(f"Sequential test {i}: {'SUCCESS' if result.success else 'FAILED'} - {result.error_message}")
        
        # Test 2: Concurrent requests (stress test)
        logger.info("\n=== Test 2: Concurrent Requests (Stress Test) ===")
        concurrent_results = await investigator.test_concurrent_requests(num_concurrent=20)
        
        # Analyze results
        logger.info("\n=== Analysis Results ===")
        
        sequential_analysis = investigator.analyze_results(sequential_results)
        concurrent_analysis = investigator.analyze_results(concurrent_results)
        
        print(f"\nSequential Test Results:")
        print(f"  Success Rate: {sequential_analysis['successful_requests']}/{sequential_analysis['total_requests']}")
        print(f"  Response ID Errors: {len(sequential_analysis['response_id_errors'])}")
        print(f"  Data Isolation Violations: {len(sequential_analysis['data_isolation_violations'])}")
        
        print(f"\nConcurrent Test Results:")
        print(f"  Success Rate: {concurrent_analysis['successful_requests']}/{concurrent_analysis['total_requests']}")
        print(f"  Response ID Errors: {len(concurrent_analysis['response_id_errors'])}")
        print(f"  Data Isolation Violations: {len(concurrent_analysis['data_isolation_violations'])}")
        print(f"  Cross-contamination Detected: {concurrent_analysis['cross_contamination_detected']}")
        print(f"  Average Response Time: {concurrent_analysis['average_response_time']:.2f}s")
        
        if concurrent_analysis['response_id_errors']:
            print(f"\nResponse ID Errors Found:")
            for error in concurrent_analysis['response_id_errors']:
                print(f"  - {error}")
        
        if concurrent_analysis['data_isolation_violations']:
            print(f"\nData Isolation Violations:")
            for violation in concurrent_analysis['data_isolation_violations']:
                print(f"  - {violation}")
        
        if concurrent_analysis['error_summary']:
            print(f"\nError Summary:")
            for error_type, count in concurrent_analysis['error_summary'].items():
                print(f"  {error_type}: {count}")
        
        # Conclusion
        print(f"\n=== CONCLUSION ===")
        if concurrent_analysis['response_id_errors']:
            print("❌ Response ID mismatch errors detected - requires investigation")
        else:
            print("✅ No Response ID mismatch errors detected")
            
        if concurrent_analysis['cross_contamination_detected']:
            print("❌ CRITICAL: Data isolation violations detected - genuine security issue")
        else:
            print("✅ No data isolation violations detected")

if __name__ == "__main__":
    asyncio.run(main())
