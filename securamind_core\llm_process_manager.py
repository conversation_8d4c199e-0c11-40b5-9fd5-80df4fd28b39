# securamind_core/llm_process_manager.py

import logging
import asyncio
import multiprocessing as mp
import queue
import time
import threading
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
from pathlib import Path

from .config import settings

# --- Initialize Logging ---
logger = logging.getLogger(__name__)

class LLMProcessStatus(Enum):
    """Status of an LLM process"""
    STARTING = "starting"
    HEALTHY = "healthy"
    CRASHED = "crashed"
    RESTARTING = "restarting"
    STOPPED = "stopped"

@dataclass
class LLMProcessConfig:
    """Configuration for an LLM process"""
    name: str
    model_path: str
    n_ctx: int
    n_gpu_layers: int
    verbose: bool
    process_type: str  # "main", "backup", "embedding"

class LLMProcess:
    """Manages a single LLM process"""

    def __init__(self, config: LLMProcessConfig):
        self.config = config
        self.status = LLMProcessStatus.STOPPED
        self.process: Optional[mp.Process] = None
        self.request_queue: Optional[mp.Queue] = None
        self.response_queue: Optional[mp.Queue] = None
        self.health_queue: Optional[mp.Queue] = None
        self.last_health_check = 0
        self.restart_count = 0
        self.max_restarts = 5
        self.restart_delay = 2.0
        # Add process lock to serialize requests (prevents streaming interference)
        self.process_lock: Optional[asyncio.Lock] = None
        # Track if the process is currently busy with a request
        self._is_busy = False
        
    def start(self):
        """Start the LLM process"""
        if self.status != LLMProcessStatus.STOPPED:
            logger.warning(f"LLM process {self.config.name} is already running")
            return
            
        logger.info(f"Starting LLM process: {self.config.name}")
        self.status = LLMProcessStatus.STARTING

        # Initialize process lock for request serialization
        self.process_lock = asyncio.Lock()

        # Create communication queues with unlimited size
        self.request_queue = mp.Queue()  # Unlimited size - requests will wait, not fail
        self.response_queue = mp.Queue()  # Unlimited size - responses will queue up
        self.health_queue = mp.Queue(maxsize=5)  # Keep health queue small
        
        # Start the process
        self.process = mp.Process(
            target=llm_worker_process,
            args=(
                self.config,
                self.request_queue,
                self.response_queue,
                self.health_queue
            ),
            name=f"LLM-{self.config.name}"
        )
        self.process.start()
        
        # Wait for initial health signal
        try:
            health_signal = self.health_queue.get(timeout=30)
            if health_signal == "ready":
                self.status = LLMProcessStatus.HEALTHY
                self.last_health_check = time.time()
                logger.info(f"LLM process {self.config.name} started successfully")
            else:
                raise Exception(f"Unexpected health signal: {health_signal}")
        except queue.Empty:
            logger.error(f"LLM process {self.config.name} failed to start (timeout)")
            self.stop()
            raise Exception(f"LLM process {self.config.name} startup timeout")
    
    def stop(self):
        """Stop the LLM process"""
        if self.process and self.process.is_alive():
            logger.info(f"Stopping LLM process: {self.config.name}")
            self.process.terminate()
            self.process.join(timeout=10)
            if self.process.is_alive():
                logger.warning(f"Force killing LLM process: {self.config.name}")
                self.process.kill()
                self.process.join()
        
        self.status = LLMProcessStatus.STOPPED
        self.process = None
        
    def is_healthy(self) -> bool:
        """Check if the process is healthy"""
        if not self.process or not self.process.is_alive():
            return False

        # Check for recent health signals
        try:
            while not self.health_queue.empty():
                health_signal = self.health_queue.get_nowait()
                if health_signal == "alive":
                    self.last_health_check = time.time()
        except queue.Empty:
            pass

        # Consider healthy if we got a signal in the last 30 seconds
        return (time.time() - self.last_health_check) < 30

    def is_available(self) -> bool:
        """Check if the process is healthy AND not busy"""
        return self.is_healthy() and not self._is_busy

    def is_busy(self) -> bool:
        """Check if the process is currently busy with a request"""
        return self._is_busy
    
    def restart(self):
        """Restart the LLM process"""
        if self.restart_count >= self.max_restarts:
            logger.error(f"LLM process {self.config.name} exceeded max restarts ({self.max_restarts})")
            self.status = LLMProcessStatus.CRASHED
            return False
            
        logger.info(f"Restarting LLM process: {self.config.name} (attempt {self.restart_count + 1})")
        self.status = LLMProcessStatus.RESTARTING
        
        self.stop()
        time.sleep(self.restart_delay)
        
        try:
            self.start()
            self.restart_count += 1
            return True
        except Exception as e:
            logger.error(f"Failed to restart LLM process {self.config.name}: {e}")
            self.status = LLMProcessStatus.CRASHED
            return False
    
    async def send_request(self, request: Dict[str, Any], timeout: float = 30.0) -> Dict[str, Any]:
        """Send a request to the LLM process and get response with serialization"""
        if not self.is_healthy():
            raise Exception(f"LLM process {self.config.name} is not healthy")

        if self.process_lock is None:
            raise Exception(f"LLM process {self.config.name} lock not initialized")

        # For embeddings, serialize the entire request-response cycle
        if request.get("action") == "embedding":
            self._is_busy = True
            try:
                async with self.process_lock:
                    try:
                        # Send request (unlimited queue - will wait, not fail)
                        self.request_queue.put(request, block=True)  # Block until space available

                        # Embeddings are not streaming - wait for direct response
                        response = await asyncio.to_thread(self.response_queue.get, timeout=timeout)

                        if isinstance(response, dict) and "error" in response:
                            raise Exception(f"LLM process error: {response['error']}")

                        return response
                    except queue.Empty:
                        raise Exception(f"LLM process {self.config.name} response timeout")
            finally:
                self._is_busy = False
        else:
            # For streaming chat completions, return a generator that holds the lock
            # This ensures the entire streaming session is serialized
            return self._locked_stream_response_generator(timeout, request)

    async def _locked_stream_response_generator(self, timeout: float, request: dict):
        """Generator that yields streaming tokens while holding the process lock for the entire duration"""
        # Mark process as busy before acquiring lock
        self._is_busy = True
        stream_completed = False
        try:
            async with self.process_lock:
                try:
                    # Send request (unlimited queue - will wait, not fail)
                    self.request_queue.put(request, block=True)  # Block until space available

                    # Wait for stream start signal
                    start_response = await asyncio.to_thread(self.response_queue.get, timeout=timeout)

                    if isinstance(start_response, dict) and "error" in start_response:
                        raise Exception(f"LLM process error: {start_response['error']}")

                    if not (isinstance(start_response, dict) and start_response.get("stream_start")):
                        raise Exception(f"Expected stream_start signal, got: {start_response}")

                    # Yield tokens until stream ends (lock held for entire duration)
                    while True:
                        token_response = await asyncio.to_thread(self.response_queue.get, timeout=timeout)

                        if isinstance(token_response, dict):
                            if "error" in token_response:
                                raise Exception(f"LLM process error: {token_response['error']}")
                            elif "stream_end" in token_response:
                                stream_completed = True
                                break
                            elif "token" in token_response:
                                # Yield token in the format expected by the streaming system
                                yield {
                                    "choices": [{
                                        "delta": {
                                            "content": token_response["token"]
                                        }
                                    }]
                                }

                except queue.Empty:
                    raise Exception(f"LLM process {self.config.name} streaming timeout")
                except asyncio.CancelledError:
                    # Client disconnected - we need to drain the remaining tokens to prevent corruption
                    logger.warning(f"Client disconnected from {self.config.name}, draining remaining tokens to prevent corruption")
                    await self._drain_remaining_tokens(timeout)
                    stream_completed = True
                    raise
        finally:
            # If stream didn't complete normally, drain remaining tokens
            if not stream_completed:
                logger.warning(f"Stream from {self.config.name} didn't complete normally, draining remaining tokens")
                try:
                    await self._drain_remaining_tokens(timeout)
                except Exception as e:
                    logger.error(f"Error draining tokens from {self.config.name}: {e}")

            # Always mark process as not busy when done
            self._is_busy = False

    async def _drain_remaining_tokens(self, timeout: float = 5.0):
        """Drain remaining tokens from the response queue to prevent corruption"""
        try:
            while True:
                token_response = await asyncio.to_thread(self.response_queue.get, timeout=timeout)

                if isinstance(token_response, dict):
                    if "stream_end" in token_response:
                        logger.info(f"Successfully drained tokens from {self.config.name} until stream_end")
                        break
                    elif "error" in token_response:
                        logger.warning(f"Error while draining tokens from {self.config.name}: {token_response['error']}")
                        break
                    # Continue draining tokens and other responses

        except queue.Empty:
            logger.warning(f"Timeout while draining tokens from {self.config.name}")
        except Exception as e:
            logger.error(f"Error draining tokens from {self.config.name}: {e}")



def llm_worker_process(
    config: LLMProcessConfig,
    request_queue: mp.Queue,
    response_queue: mp.Queue,
    health_queue: mp.Queue
):
    """Worker process that runs the LLM"""
    try:
        # Import llama_cpp inside the worker process
        from llama_cpp import Llama
        
        logger.info(f"Loading LLM in worker process: {config.name}")
        
        # Load the LLM
        llm_kwargs = {
            "model_path": config.model_path,
            "n_ctx": config.n_ctx,
            "n_gpu_layers": config.n_gpu_layers,
            "verbose": config.verbose
        }

        # Add embedding=True for embedding models
        if config.process_type == "embedding":
            llm_kwargs["embedding"] = True

        llm = Llama(**llm_kwargs)
        
        # Signal that we're ready
        health_queue.put("ready")
        logger.info(f"LLM worker {config.name} is ready")
        
        # Health check thread
        def health_checker():
            while True:
                try:
                    health_queue.put("alive", timeout=1)
                    time.sleep(10)  # Send health signal every 10 seconds
                except:
                    break
        
        health_thread = threading.Thread(target=health_checker, daemon=True)
        health_thread.start()
        
        # Main request processing loop
        while True:
            try:
                # Get request
                request = request_queue.get(timeout=1)
                
                if request.get("action") == "shutdown":
                    logger.info(f"LLM worker {config.name} received shutdown signal")
                    break
                
                # Process request
                if request.get("action") == "chat_completion":
                    # All SecuraMind requests are streaming-only
                    try:
                        stream = llm.create_chat_completion(
                            messages=request["messages"],
                            max_tokens=request.get("max_tokens", 1024),
                            temperature=request.get("temperature", 0.7),
                            stream=True
                        )

                        # Send stream start signal
                        response_queue.put({"stream_start": True})

                        # Process stream and send tokens
                        for chunk in stream:
                            if "choices" in chunk and chunk["choices"]:
                                delta = chunk["choices"][0].get("delta", {})
                                content = delta.get("content")
                                if content:
                                    response_queue.put({"token": content})

                        # Send stream end signal
                        response_queue.put({"stream_end": True})

                    except Exception as e:
                        response_queue.put({"error": f"Streaming error: {str(e)}"})

                elif request.get("action") == "embedding":
                    response = llm.create_embedding(request["texts"])
                    # Ensure response is in dict format
                    if not isinstance(response, dict):
                        response = {"data": response}
                    response_queue.put(response)

                else:
                    response_queue.put({"error": f"Unknown action: {request.get('action')}"})

            except queue.Empty:
                continue  # Timeout, continue loop
            except Exception as e:
                logger.error(f"Error in LLM worker {config.name}: {e}", exc_info=True)
                response_queue.put({"error": str(e)})
                
    except Exception as e:
        logger.error(f"Fatal error in LLM worker {config.name}: {e}", exc_info=True)
        health_queue.put(f"error: {str(e)}")

class LLMProcessManager:
    """Manages multiple LLM processes"""
    
    def __init__(self):
        self.processes: Dict[str, LLMProcess] = {}
        self.health_monitor_task: Optional[asyncio.Task] = None
        self.running = False
        
    async def start(self):
        """Start all LLM processes"""
        logger.info("Starting LLM Process Manager")
        self.running = True
        
        # Create process configurations
        configs = self._create_process_configs()
        
        # Start each process
        for config in configs:
            try:
                process = LLMProcess(config)
                process.start()
                self.processes[config.name] = process
                logger.info(f"Started LLM process: {config.name}")
            except Exception as e:
                logger.error(f"Failed to start LLM process {config.name}: {e}")
        
        # Start health monitoring
        self.health_monitor_task = asyncio.create_task(self._health_monitor())
        
    async def stop(self):
        """Stop all LLM processes"""
        logger.info("Stopping LLM Process Manager")
        self.running = False
        
        if self.health_monitor_task:
            self.health_monitor_task.cancel()
            try:
                await self.health_monitor_task
            except asyncio.CancelledError:
                pass
        
        for process in self.processes.values():
            process.stop()
        
        self.processes.clear()
        
    def _create_process_configs(self) -> List[LLMProcessConfig]:
        """Create configurations for all LLM processes"""
        configs = []
        
        # Main LLM
        main_model_path = settings.absolute_models_dir / settings.model_filename
        if main_model_path.exists():
            configs.append(LLMProcessConfig(
                name="main",
                model_path=str(main_model_path),
                n_ctx=settings.n_ctx,
                n_gpu_layers=settings.n_gpu_layers,
                verbose=settings.verbose_llm,
                process_type="main"
            ))
        
        # Backup LLM
        if settings.backup_llm_enabled:
            backup_model_path = settings.absolute_models_dir / settings.backup_model_filename
            if backup_model_path.exists():
                configs.append(LLMProcessConfig(
                    name="backup",
                    model_path=str(backup_model_path),
                    n_ctx=settings.backup_n_ctx,
                    n_gpu_layers=settings.backup_n_gpu_layers,
                    verbose=settings.backup_verbose_llm,
                    process_type="backup"
                ))
        
        # Embedding LLM
        embedding_model_path = settings.absolute_models_dir / settings.embedding_model_filename
        if embedding_model_path.exists():
            configs.append(LLMProcessConfig(
                name="embedding",
                model_path=str(embedding_model_path),
                n_ctx=2048,  # Embeddings don't need large context
                n_gpu_layers=settings.embedding_model_n_gpu_layers,
                verbose=settings.embedding_model_verbose,
                process_type="embedding"
            ))
        
        return configs
    
    async def _health_monitor(self):
        """Monitor health of all processes and restart if needed"""
        while self.running:
            try:
                for name, process in list(self.processes.items()):
                    if not process.is_healthy():
                        logger.warning(f"LLM process {name} is unhealthy, attempting restart")
                        if not process.restart():
                            logger.error(f"Failed to restart LLM process {name}")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor: {e}", exc_info=True)
                await asyncio.sleep(5)
    
    def get_process(self, name: str) -> Optional[LLMProcess]:
        """Get a specific LLM process"""
        return self.processes.get(name)
    
    def get_process_status(self, name: str) -> str:
        """Get the status of a specific process"""
        process = self.processes.get(name)
        if process:
            return process.status.value
        return "not_found"
    
    def get_all_status(self) -> Dict[str, str]:
        """Get status of all processes"""
        return {name: process.status.value for name, process in self.processes.items()}

# Global instance
llm_process_manager = LLMProcessManager()
