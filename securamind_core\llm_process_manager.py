# securamind_core/llm_process_manager.py

import logging
import asyncio
import multiprocessing as mp
import queue
import time
import threading
import uuid
from typing import Dict, Any, Optional, List, AsyncGenerator
from enum import Enum
from dataclasses import dataclass
from pathlib import Path

from .config import settings

# --- Initialize Logging ---
logger = logging.getLogger(__name__)

class LLMProcessStatus(Enum):
    """Status of an LLM process"""
    STARTING = "starting"
    HEALTHY = "healthy"
    CRASHED = "crashed"
    RESTARTING = "restarting"
    STOPPED = "stopped"

@dataclass
class LLMProcessConfig:
    """Configuration for an LLM process"""
    name: str
    model_path: str
    n_ctx: int
    n_gpu_layers: int
    verbose: bool
    process_type: str  # "main", "backup", "embedding"

class LLMProcess:
    """Manages a single LLM process with direct response routing"""

    def __init__(self, config: LLMProcessConfig):
        self.config = config
        self.status = LLMProcessStatus.STOPPED
        self.process: Optional[mp.Process] = None
        self.request_queue: Optional[mp.Queue] = None
        self.health_queue: Optional[mp.Queue] = None
        self.last_health_check = 0
        self.restart_count = 0
        self.max_restarts = 5
        self.restart_delay = 2.0

        # Direct response routing - each request gets its own response channel
        self.response_channels: Dict[str, asyncio.Queue] = {}
        self.response_channels_lock = asyncio.Lock()

        # Manager for sharing response routing with worker process
        self.manager: Optional[mp.Manager] = None
        self.shared_response_queue: Optional[mp.Queue] = None
        
    def start(self):
        """Start the LLM process with direct response routing"""
        if self.status != LLMProcessStatus.STOPPED:
            logger.warning(f"LLM process {self.config.name} is already running")
            return

        logger.info(f"Starting LLM process: {self.config.name}")
        self.status = LLMProcessStatus.STARTING

        # Create communication queues
        self.request_queue = mp.Queue()  # Single request queue
        self.shared_response_queue = mp.Queue()  # Shared response queue with request ID routing
        self.health_queue = mp.Queue(maxsize=5)  # Keep health queue small

        # Start response router task
        asyncio.create_task(self._response_router())

        # Start the process with direct routing
        self.process = mp.Process(
            target=llm_worker_process_with_direct_routing,
            args=(
                self.config,
                self.request_queue,
                self.shared_response_queue,
                self.health_queue
            ),
            name=f"LLM-{self.config.name}"
        )
        self.process.start()

        # Wait for initial health signal
        try:
            health_signal = self.health_queue.get(timeout=30)
            if health_signal == "ready":
                self.status = LLMProcessStatus.HEALTHY
                self.last_health_check = time.time()
                logger.info(f"LLM process {self.config.name} started successfully")
            else:
                raise Exception(f"Unexpected health signal: {health_signal}")
        except queue.Empty:
            logger.error(f"LLM process {self.config.name} failed to start (timeout)")
            self.stop()
            raise Exception(f"LLM process {self.config.name} startup timeout")
    
    def stop(self):
        """Stop the LLM process and clean up resources"""
        if self.process and self.process.is_alive():
            logger.info(f"Stopping LLM process: {self.config.name}")
            self.process.terminate()
            self.process.join(timeout=10)
            if self.process.is_alive():
                logger.warning(f"Force killing LLM process: {self.config.name}")
                self.process.kill()
                self.process.join()

        # Clean up resources
        self.status = LLMProcessStatus.STOPPED
        self.process = None
        self.response_channels.clear()
        self.shared_response_queue = None
        
    def is_healthy(self) -> bool:
        """Check if the process is healthy"""
        if not self.process or not self.process.is_alive():
            return False

        # Check for recent health signals
        try:
            while not self.health_queue.empty():
                health_signal = self.health_queue.get_nowait()
                if health_signal == "alive":
                    self.last_health_check = time.time()
        except queue.Empty:
            pass

        # Consider healthy if we got a signal in the last 30 seconds
        return (time.time() - self.last_health_check) < 30

    def is_available(self) -> bool:
        """Check if the process is healthy"""
        return self.is_healthy()

    def get_active_request_count(self) -> int:
        """Get the number of currently active requests"""
        return len(self.response_channels)
    
    def restart(self):
        """Restart the LLM process"""
        if self.restart_count >= self.max_restarts:
            logger.error(f"LLM process {self.config.name} exceeded max restarts ({self.max_restarts})")
            self.status = LLMProcessStatus.CRASHED
            return False
            
        logger.info(f"Restarting LLM process: {self.config.name} (attempt {self.restart_count + 1})")
        self.status = LLMProcessStatus.RESTARTING
        
        self.stop()
        time.sleep(self.restart_delay)
        
        try:
            self.start()
            self.restart_count += 1
            return True
        except Exception as e:
            logger.error(f"Failed to restart LLM process {self.config.name}: {e}")
            self.status = LLMProcessStatus.CRASHED
            return False
    
    async def send_request(self, request: Dict[str, Any]) -> Any:
        """Send a request to the LLM process using direct response routing"""
        if not self.is_healthy():
            raise Exception(f"LLM process {self.config.name} is not healthy")

        # Generate unique request ID
        request_id = str(uuid.uuid4())
        request["request_id"] = request_id

        # Create dedicated response channel for this request
        response_channel = asyncio.Queue()

        async with self.response_channels_lock:
            self.response_channels[request_id] = response_channel

        try:
            # Send request to worker process
            self.request_queue.put(request, block=True)

            if request.get("action") == "embedding":
                # For embeddings, wait for single response
                return await self._get_embedding_response_direct(request_id, response_channel)
            else:
                # For streaming chat completions, return generator
                return self._stream_response_generator_direct(request_id, response_channel)

        except Exception as e:
            # Clean up on error
            await self._cleanup_request_channel(request_id)
            raise e

    async def _get_embedding_response_direct(self, request_id: str, response_channel: asyncio.Queue) -> Dict[str, Any]:
        """Get embedding response from dedicated channel"""
        try:
            # Wait for response (no timeout - direct channel ensures completion)
            response = await response_channel.get()

            if isinstance(response, dict) and "error" in response:
                raise Exception(f"LLM process error: {response['error']}")

            return response

        finally:
            # Always clean up
            await self._cleanup_request_channel(request_id)

    async def _stream_response_generator_direct(self, request_id: str, response_channel: asyncio.Queue) -> AsyncGenerator[Dict[str, Any], None]:
        """Generator that yields streaming tokens from dedicated channel"""
        try:
            # Wait for stream start signal
            start_response = await response_channel.get()

            if isinstance(start_response, dict) and "error" in start_response:
                raise Exception(f"LLM process error: {start_response['error']}")

            if not (isinstance(start_response, dict) and start_response.get("stream_start")):
                raise Exception(f"Expected stream_start signal, got: {start_response}")

            # Yield tokens until stream ends
            while True:
                token_response = await response_channel.get()

                if isinstance(token_response, dict):
                    if "error" in token_response:
                        raise Exception(f"LLM process error: {token_response['error']}")
                    elif "stream_end" in token_response:
                        break
                    elif "token" in token_response:
                        # Yield token in the format expected by the streaming system
                        yield {
                            "choices": [{
                                "delta": {
                                    "content": token_response["token"]
                                }
                            }]
                        }

        except asyncio.CancelledError:
            # Client disconnected - just clean up, no draining needed!
            logger.info(f"Client disconnected from {self.config.name} request {request_id}")
            raise
        finally:
            # Always clean up this request's resources
            await self._cleanup_request_channel(request_id)

    async def _response_router(self):
        """Route responses from shared queue to correct request channels"""
        while self.status != LLMProcessStatus.STOPPED:
            try:
                if not self.shared_response_queue:
                    await asyncio.sleep(0.1)
                    continue

                # Get response from shared queue (non-blocking)
                try:
                    response = await asyncio.to_thread(self.shared_response_queue.get, timeout=0.1)
                except queue.Empty:
                    continue

                # Route response to correct channel
                if isinstance(response, dict) and "request_id" in response:
                    request_id = response["request_id"]

                    async with self.response_channels_lock:
                        if request_id in self.response_channels:
                            # Remove request_id from response before sending to channel
                            clean_response = {k: v for k, v in response.items() if k != "request_id"}
                            await self.response_channels[request_id].put(clean_response)
                        else:
                            logger.warning(f"Received response for unknown request_id: {request_id}")
                else:
                    logger.warning(f"Received response without request_id: {response}")

            except Exception as e:
                logger.error(f"Error in response router for {self.config.name}: {e}", exc_info=True)
                await asyncio.sleep(1)

    async def _cleanup_request_channel(self, request_id: str):
        """Clean up resources for a specific request"""
        async with self.response_channels_lock:
            # Remove from tracking
            self.response_channels.pop(request_id, None)



def llm_worker_process_with_direct_routing(
    config: LLMProcessConfig,
    request_queue: mp.Queue,
    shared_response_queue: mp.Queue,
    health_queue: mp.Queue
):
    """Worker process that runs the LLM with direct response routing"""
    try:
        # Import llama_cpp inside the worker process
        from llama_cpp import Llama

        logger.info(f"Loading LLM in worker process: {config.name}")

        # Load the LLM
        llm_kwargs = {
            "model_path": config.model_path,
            "n_ctx": config.n_ctx,
            "n_gpu_layers": config.n_gpu_layers,
            "verbose": config.verbose
        }

        # Add embedding=True for embedding models
        if config.process_type == "embedding":
            llm_kwargs["embedding"] = True

        llm = Llama(**llm_kwargs)

        # Signal that we're ready
        health_queue.put("ready")
        logger.info(f"LLM worker {config.name} is ready")

        # Health check thread
        def health_checker():
            while True:
                try:
                    health_queue.put("alive", timeout=1)
                    time.sleep(10)  # Send health signal every 10 seconds
                except:
                    break

        health_thread = threading.Thread(target=health_checker, daemon=True)
        health_thread.start()

        # Main request processing loop
        while True:
            try:
                # Get request
                request = request_queue.get(timeout=1)

                if request.get("action") == "shutdown":
                    logger.info(f"LLM worker {config.name} received shutdown signal")
                    break

                # Get request ID for direct routing
                request_id = request.get("request_id")
                if not request_id:
                    logger.error(f"Missing request_id in request")
                    continue

                # Process request and route responses directly to the correct channel
                if request.get("action") == "chat_completion":
                    _process_chat_completion_direct(llm, request, request_id, shared_response_queue, config.name)
                elif request.get("action") == "embedding":
                    _process_embedding_direct(llm, request, request_id, shared_response_queue, config.name)
                else:
                    _send_response_direct(request_id, shared_response_queue, {"error": f"Unknown action: {request.get('action')}"})

            except queue.Empty:
                continue  # Timeout, continue loop
            except Exception as e:
                logger.error(f"Error in LLM worker {config.name}: {e}", exc_info=True)

    except Exception as e:
        logger.error(f"Fatal error in LLM worker {config.name}: {e}", exc_info=True)
        health_queue.put(f"error: {str(e)}")


def _send_response_direct(request_id: str, response_queue: mp.Queue, response: Dict[str, Any]):
    """Send response directly to shared queue with request ID"""
    response["request_id"] = request_id
    response_queue.put(response)


def _process_chat_completion_direct(llm, request: Dict[str, Any], request_id: str, response_queue: mp.Queue, worker_name: str):
    """Process chat completion request and route responses with request ID"""
    try:
        stream = llm.create_chat_completion(
            messages=request["messages"],
            max_tokens=request.get("max_tokens", 1024),
            temperature=request.get("temperature", 0.7),
            stream=True
        )

        # Send stream start signal with request ID
        _send_response_direct(request_id, response_queue, {"stream_start": True})

        # Process stream and send tokens with request ID
        for chunk in stream:
            if "choices" in chunk and chunk["choices"]:
                delta = chunk["choices"][0].get("delta", {})
                content = delta.get("content")
                if content:
                    _send_response_direct(request_id, response_queue, {"token": content})

        # Send stream end signal with request ID
        _send_response_direct(request_id, response_queue, {"stream_end": True})

    except Exception as e:
        logger.error(f"Error in chat completion for worker {worker_name}: {e}", exc_info=True)
        _send_response_direct(request_id, response_queue, {"error": f"Streaming error: {str(e)}"})


def _process_embedding_direct(llm, request: Dict[str, Any], request_id: str, response_queue: mp.Queue, worker_name: str):
    """Process embedding request and route response with request ID"""
    try:
        response = llm.create_embedding(request["texts"])
        # Ensure response is in dict format and add request ID
        if not isinstance(response, dict):
            response = {"data": response}
        _send_response_direct(request_id, response_queue, response)

    except Exception as e:
        logger.error(f"Error in embedding for worker {worker_name}: {e}", exc_info=True)
        _send_response_direct(request_id, response_queue, {"error": f"Embedding error: {str(e)}"})

class LLMProcessManager:
    """Manages multiple LLM processes"""
    
    def __init__(self):
        self.processes: Dict[str, LLMProcess] = {}
        self.health_monitor_task: Optional[asyncio.Task] = None
        self.running = False
        
    async def start(self):
        """Start all LLM processes"""
        logger.info("Starting LLM Process Manager")
        self.running = True
        
        # Create process configurations
        configs = self._create_process_configs()
        
        # Start each process
        for config in configs:
            try:
                process = LLMProcess(config)
                process.start()
                self.processes[config.name] = process
                logger.info(f"Started LLM process: {config.name}")
            except Exception as e:
                logger.error(f"Failed to start LLM process {config.name}: {e}")
        
        # Start health monitoring
        self.health_monitor_task = asyncio.create_task(self._health_monitor())
        
    async def stop(self):
        """Stop all LLM processes"""
        logger.info("Stopping LLM Process Manager")
        self.running = False
        
        if self.health_monitor_task:
            self.health_monitor_task.cancel()
            try:
                await self.health_monitor_task
            except asyncio.CancelledError:
                pass
        
        for process in self.processes.values():
            process.stop()
        
        self.processes.clear()
        
    def _create_process_configs(self) -> List[LLMProcessConfig]:
        """Create configurations for all LLM processes"""
        configs = []
        
        # Main LLM
        main_model_path = settings.absolute_models_dir / settings.model_filename
        if main_model_path.exists():
            configs.append(LLMProcessConfig(
                name="main",
                model_path=str(main_model_path),
                n_ctx=settings.n_ctx,
                n_gpu_layers=settings.n_gpu_layers,
                verbose=settings.verbose_llm,
                process_type="main"
            ))
        
        # Backup LLM
        if settings.backup_llm_enabled:
            backup_model_path = settings.absolute_models_dir / settings.backup_model_filename
            if backup_model_path.exists():
                configs.append(LLMProcessConfig(
                    name="backup",
                    model_path=str(backup_model_path),
                    n_ctx=settings.backup_n_ctx,
                    n_gpu_layers=settings.backup_n_gpu_layers,
                    verbose=settings.backup_verbose_llm,
                    process_type="backup"
                ))
        
        # Embedding LLM
        embedding_model_path = settings.absolute_models_dir / settings.embedding_model_filename
        if embedding_model_path.exists():
            configs.append(LLMProcessConfig(
                name="embedding",
                model_path=str(embedding_model_path),
                n_ctx=2048,  # Embeddings don't need large context
                n_gpu_layers=settings.embedding_model_n_gpu_layers,
                verbose=settings.embedding_model_verbose,
                process_type="embedding"
            ))
        
        return configs
    
    async def _health_monitor(self):
        """Monitor health of all processes and restart if needed"""
        while self.running:
            try:
                for name, process in list(self.processes.items()):
                    if not process.is_healthy():
                        logger.warning(f"LLM process {name} is unhealthy, attempting restart")
                        if not process.restart():
                            logger.error(f"Failed to restart LLM process {name}")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor: {e}", exc_info=True)
                await asyncio.sleep(5)
    
    def get_process(self, name: str) -> Optional[LLMProcess]:
        """Get a specific LLM process"""
        return self.processes.get(name)
    
    def get_process_status(self, name: str) -> str:
        """Get the status of a specific process"""
        process = self.processes.get(name)
        if process:
            return process.status.value
        return "not_found"
    
    def get_all_status(self) -> Dict[str, str]:
        """Get status of all processes"""
        return {name: process.status.value for name, process in self.processes.items()}

# Global instance
llm_process_manager = LLMProcessManager()
