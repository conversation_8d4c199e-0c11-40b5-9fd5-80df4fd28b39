# securamind_core/main.py

def ensure_non_root_execution():
    """Ensure the application is not running with root/admin privileges"""
    import os
    import sys
    
    if os.name == 'nt':  # Windows
        import ctypes
        if ctypes.windll.shell32.IsUserAnAdmin() != 0:
            print("Error: Application must not run with administrator privileges", file=sys.stderr)
            sys.exit(1)
    else:  # Unix-like
        if os.geteuid() == 0:
            print("Error: Application must not run with root privileges", file=sys.stderr)
            sys.exit(1)

# Call this before any other code executes
ensure_non_root_execution()

import logging
import asyncio

from fastapi import FastAPI
from qdrant_client import QdrantClient

from .config import settings, setup_logging
from .ingestion import router as ingestion_router
from .inference import router as inference_router
from .llm_process_manager import llm_process_manager

# --- Initialize Logging ---
setup_logging(settings.log_level)
logger = logging.getLogger(__name__)

# --- Global instances ---
qdrant_client_instance: QdrantClient = None

# --- Lifespan Event Handler ---
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Why: Handle application startup and shutdown using modern FastAPI lifespan events.
    How: Starts LLM processes and initializes clients on startup, cleans up on shutdown.
    """
    global qdrant_client_instance

    # Startup
    logger.info("Mindend startup sequence initiated.")

    # Start LLM Process Manager
    logger.info("Starting LLM processes...")
    try:
        await llm_process_manager.start()
        logger.info("LLM processes started successfully.")
    except Exception as e:
        logger.error(f"Error starting LLM processes: {e}", exc_info=True)

    # Initialize Qdrant Client
    logger.info(f"Initializing Qdrant client with path: {settings.absolute_qdrant_path}")
    try:
        qdrant_client_instance = QdrantClient(path=str(settings.absolute_qdrant_path))
        logger.info("Qdrant client initialized successfully.")
    except Exception as e:
        logger.error(f"Error initializing Qdrant client: {e}", exc_info=True)

    # Start periodic cleanup task for ingestion progress
    from .ingestion import periodic_cleanup
    cleanup_task = asyncio.create_task(periodic_cleanup())
    logger.info("Started periodic ingestion progress cleanup task.")

    # Yield control to the application
    yield

    # Cancel cleanup task on shutdown
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        logger.info("Periodic cleanup task cancelled.")
    except Exception as e:
        logger.error(f"Error cancelling cleanup task: {e}", exc_info=True)

    # Shutdown
    logger.info("Application shutdown sequence initiated.")

    # Stop LLM Process Manager
    try:
        await llm_process_manager.stop()
        logger.info("LLM processes stopped.")
    except Exception as e:
        logger.error(f"Error stopping LLM processes: {e}", exc_info=True)

    if qdrant_client_instance:
        try:
            qdrant_client_instance.close()
            logger.info("Qdrant client closed.")
        except Exception as e:
            logger.error(f"Error closing Qdrant client: {e}", exc_info=True)

# --- FastAPI App Initialization ---
app = FastAPI(
    title="SecuraMind Mindend",
    description="API for local LLM inference and data ingestion.",
    version="0.3.0",
    lifespan=lifespan
)

# Include routers
app.include_router(ingestion_router)
app.include_router(inference_router)

# Import and include status router
from .ingestion import status_router
app.include_router(status_router)

# --- LLM Routing Functions ---
async def check_llm_availability():
    """
    Check which LLM processes are available for processing.
    Returns tuple: (main_available, backup_available)
    """
    main_process = llm_process_manager.get_process("main")
    backup_process = llm_process_manager.get_process("backup")

    main_available = main_process is not None and main_process.is_available()
    backup_available = backup_process is not None and backup_process.is_available()

    return main_available, backup_available

async def get_best_available_llm_process():
    """
    Get the best available LLM process for request routing.
    Priority: Main LLM first, then Backup LLM if main is busy.
    If both are busy, still return main LLM for queueing (requests will wait in queue).
    Returns tuple: (process, llm_type) - always returns a process for queueing
    """
    main_available, backup_available = await check_llm_availability()

    # Debug logging to track routing decisions
    main_process = llm_process_manager.get_process("main")
    backup_process = llm_process_manager.get_process("backup")

    if main_process:
        logger.info(f"Main LLM: healthy={main_process.is_healthy()}, busy={main_process.is_busy()}, available={main_available}")
    if backup_process:
        logger.info(f"Backup LLM: healthy={backup_process.is_healthy()}, busy={backup_process.is_busy()}, available={backup_available}")

    if main_available:
        logger.info("Routing to main LLM process (preferred)")
        return llm_process_manager.get_process("main"), "main"
    elif backup_available:
        logger.info("Main LLM busy, routing to backup LLM process")
        return llm_process_manager.get_process("backup"), "backup"
    else:
        # Both LLMs are busy - queue request on main LLM (it has unlimited queue)
        logger.info("Both LLMs busy, queueing request on main LLM process")
        if main_process and main_process.is_healthy():
            return main_process, "main"
        elif backup_process and backup_process.is_healthy():
            return backup_process, "backup"
        else:
            logger.error("No healthy LLM processes available")
            return None, None

# --- Process-Safe LLM Access Functions ---
async def safe_llm_create_chat_completion(messages, max_tokens, temperature):
    """
    Process-safe wrapper for LLM chat completion with intelligent routing.
    Prevents GGML_ASSERT failures by using isolated LLM processes.

    Routes to main LLM first, falls back to backup LLM if main is busy.
    If both are busy, queues request on main LLM (unlimited queue).
    Uses IPC to communicate with isolated LLM processes.
    """
    # Get the best available LLM process (always returns a process for queueing)
    selected_process, llm_type = await get_best_available_llm_process()

    if selected_process is None:
        raise RuntimeError("No healthy LLM processes are available")

    logger.debug(f"Using {llm_type} LLM process for chat completion")

    # Prepare request for the LLM process
    request = {
        "action": "chat_completion",
        "messages": messages,
        "max_tokens": max_tokens,
        "temperature": temperature
        # All SecuraMind requests are streaming-only
    }

    try:
        # Send request to LLM process and get response
        response = await selected_process.send_request(request, timeout=60.0)
        return response
    except Exception as e:
        logger.error(f"Error in {llm_type} LLM process communication: {e}")
        raise RuntimeError(f"LLM process communication failed: {str(e)}")

async def safe_embedding_llm_create_embedding(texts):
    """
    Process-safe wrapper for embedding LLM.
    Prevents GGML_ASSERT failures by using isolated embedding process.
    """
    embedding_process = llm_process_manager.get_process("embedding")

    if embedding_process is None or not embedding_process.is_healthy():
        raise RuntimeError("Embedding LLM process is not available")

    logger.debug("Using embedding LLM process for embedding creation")

    # Prepare request for the embedding process
    request = {
        "action": "embedding",
        "texts": texts
    }

    try:
        # Send request to embedding process and get response
        # Embedding requests are not streaming, so we expect a direct response
        response = await embedding_process.send_request(request, timeout=30.0)
        return response
    except Exception as e:
        logger.error(f"Error in embedding LLM process communication: {e}")
        raise RuntimeError(f"Embedding process communication failed: {str(e)}")

# --- Health Check (health_check) ---

@app.get("/api/v1/health")
async def health_check():
    """
    Why: Provide a basic health check for the service with process status.
    """
    logger.debug("Health check requested.")

    # Get process statuses
    main_llm_status = llm_process_manager.get_process_status("main")
    backup_llm_status = llm_process_manager.get_process_status("backup")
    embedding_llm_status = llm_process_manager.get_process_status("embedding")

    # Convert status to health check format
    def status_to_health(status):
        if status == "healthy":
            return "process_healthy"
        elif status in ["starting", "restarting"]:
            return "process_starting"
        elif status == "crashed":
            return "process_crashed"
        elif status == "stopped":
            return "process_stopped"
        else:
            return "not_found"

    main_llm_health = status_to_health(main_llm_status)
    backup_llm_health = status_to_health(backup_llm_status)
    embedding_llm_health = status_to_health(embedding_llm_status)

    qdrant_status = "initialized" if qdrant_client_instance else "not_initialized_or_error"

    # Check current availability
    main_available, backup_available = await check_llm_availability()

    return {
        "status": "ok",
        "main_llm_status": main_llm_health,
        "backup_llm_status": backup_llm_health,
        "embedding_llm_status": embedding_llm_health,
        "qdrant_status": qdrant_status,
        "main_llm_available": main_available,
        "backup_llm_available": backup_available,
        "concurrent_capacity": sum([main_available, backup_available]),
        "backup_llm_enabled": settings.backup_llm_enabled,
        "main_llm_model_configured": str(settings.absolute_models_dir / settings.model_filename),
        "backup_llm_model_configured": str(settings.absolute_models_dir / settings.backup_model_filename),
        "embedding_model_configured": str(settings.absolute_models_dir / settings.embedding_model_filename),
        "qdrant_path_configured": str(settings.absolute_qdrant_path)
    }

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting Uvicorn server on {settings.app_host}:{settings.app_port}")
    uvicorn.run(
        "securamind_core.main:app",
        host=settings.app_host,
        port=settings.app_port,
        log_level=settings.log_level.lower(),
    )
